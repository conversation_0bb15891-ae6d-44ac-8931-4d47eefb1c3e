import React from "react";
import Button from "./Button";

interface HeroProps {
  title: string;
  subtitle: string;
  primaryButtonText?: string;
  primaryButtonHref?: string;
  secondaryButtonText?: string;
  secondaryButtonHref?: string;
  backgroundImage?: string;
}

const Hero = ({
  title,
  subtitle,
  primaryButtonText,
  primaryButtonHref,
  secondaryButtonText,
  secondaryButtonHref,
  backgroundImage = '/assets/image-3.jpg',
}: HeroProps) => {
  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 z-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('${backgroundImage}')`
        }}
      ></div>

      {/* Background overlays */}
      <div className="absolute inset-0 z-10">
        <div className="absolute inset-0 bg-black opacity-60"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black/20 via-transparent to-black/80"></div>

        {/* Subtle animated grid lines (reduced opacity) */}
        <div className="absolute inset-0 flex flex-col opacity-5">
          {Array.from({ length: 15 }).map((_, i) => (
            <div key={`h-${i}`} className="h-px bg-neon-orange w-full animate-pulse-slow" style={{ marginTop: `${i * 7}vh`, animationDelay: `${i * 0.2}s` }}></div>
          ))}
        </div>
        <div className="absolute inset-0 flex flex-row opacity-5">
          {Array.from({ length: 15 }).map((_, i) => (
            <div key={`v-${i}`} className="w-px bg-neon-orange h-full animate-pulse-slow" style={{ marginLeft: `${i * 7}vw`, animationDelay: `${i * 0.2}s` }}></div>
          ))}
        </div>

        {/* Subtle glowing orbs (reduced and repositioned) */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-neon-orange/10 blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full bg-neon-red/10 blur-3xl animate-float" style={{ animationDelay: '3s' }}></div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 md:px-6 relative z-20 text-center flex flex-col justify-center min-h-[75vh] pt-20 md:pt-36">
        <div className="space-y-4 md:space-y-6">
          <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-display font-bold tracking-wider text-white leading-tight">
            <span className="inline-block">{title}</span>
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed px-2">
            {subtitle}
          </p>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-6 md:bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-20">
        {/* Mobile: Swipe indicator */}
        <div className="block md:hidden">
          <span className="text-white text-xs mb-2">Swipe Up</span>
          <div className="flex items-center justify-center">
            <div className="w-8 h-8 flex items-center justify-center">
              <svg className="w-6 h-6 text-white animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                {/* Finger */}
                <path d="M12 2c-1.1 0-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V8c0-1.1-.9-2-2-2s-2 .9-2 2v4c0 .55-.45 1-1 1s-1-.45-1-1V10c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 3.31 2.69 6 6 6h6c3.31 0 6-2.69 6-6V4c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V4c0-1.1-.9-2-2-2z"/>
                {/* Upward arrow */}
                <path d="M12 8l-3 3h2v4h2v-4h2l-3-3z" fill="white" opacity="0.8"/>
              </svg>
            </div>
          </div>
        </div>

        {/* Desktop: Mouse scroll indicator */}
        <div className="hidden md:flex md:flex-col md:items-center">
          <span className="text-white text-sm mb-2">Scroll Down</span>
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center items-start pt-2">
            <div className="w-1 h-2 bg-white rounded-full animate-bounce"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
