import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import FeatureCard from "@/components/FeatureCard";
import Button from "@/components/Button";
import BouncingShapes from "@/components/BouncingShapes";

export default function Home() {
  return (
    <>
      <Navbar />

      {/* Hero Section */}
      <Hero
        title="WELCOME TO TGRS"
        subtitle="Experience immersive roleplay in a unique Telugu community with custom features and scripts"
        primaryButtonText="Join Discord"
        primaryButtonHref="https://discord.gg/GAMravHDnB"
        secondaryButtonText="Learn More"
        secondaryButtonHref="#features"
      />

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* Character Showcase Section */}
      <section id="about" className="py-8 md:py-20 relative overflow-hidden min-h-screen">
        {/* Background Video */}
        <div className="absolute inset-0 w-full h-full">
          {/* Desktop Video */}
          <iframe
            src="https://www.youtube.com/embed/pZbzoJAj4TE?autoplay=1&mute=1&loop=1&playlist=pZbzoJAj4TE&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&disablekb=1&fs=0&cc_load_policy=0&start=33"
            className="w-full h-full hidden md:block"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              width: '100vw',
              height: '56.25vw', // 16:9 aspect ratio
              minHeight: '100vh',
              minWidth: '177.77vh', // 16:9 aspect ratio
              transform: 'translate(-50%, -50%)',
              pointerEvents: 'none',
              border: 'none'
            }}
            allow="autoplay; encrypted-media"
            allowFullScreen={false}
          />

          {/* Mobile Video */}
          <div className="block md:hidden absolute inset-0 w-full h-full">
            <iframe
              src="https://www.youtube.com/embed/waN7QSRC-YU?autoplay=1&mute=1&loop=1&playlist=waN7QSRC-YU&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&disablekb=1&fs=0&cc_load_policy=0&start=0&end=45&playsinline=1&enablejsapi=0"
              className="absolute"
              style={{
                top: '-50%',
                left: '-50%',
                width: '200%',
                height: '200%',
                minHeight: '200%',
                minWidth: '200%',
                pointerEvents: 'none',
                border: 'none',
                objectFit: 'cover'
              }}
              allow="autoplay; encrypted-media"
              allowFullScreen={false}
            />
          </div>

          {/* Dark overlay */}
          <div className="absolute inset-0 bg-black/60"></div>
        </div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-6 sm:mb-8 md:mb-12 lg:mb-16">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-display font-bold text-white mb-3 sm:mb-4 wave-heading-bg">
              Create Your <span className="text-neon-orange">Unique Story</span>
            </h2>
            <p className="text-xs sm:text-sm md:text-base lg:text-lg text-gray-400 max-w-3xl mx-auto px-2">
              Step into Los Santos and become anyone you want to be. Your story, your rules.
            </p>
          </div>

          {/* Character Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6 lg:gap-8 mb-6 sm:mb-8 md:mb-12 lg:mb-16 px-2 sm:px-0">
            {/* Criminal Character */}
            <div className="glass-enhanced rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-5 lg:p-6 border border-red-500/30 hover:border-red-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-circuit">
              <div className="absolute inset-0 bg-black/20 rounded-xl"></div>

              <div className="relative z-10 flex flex-col h-full">
                <div className="mb-3 text-red-500 group-hover:text-white transition-all duration-300 relative icon-container">
                  <div className="w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center">
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                      {/* Top particles */}
                      <div className="absolute w-1 h-1 bg-red-500 rounded-full animate-ping" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-red-400 rounded-full animate-pulse" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>

                      {/* Right particles */}
                      <div className="absolute w-1 h-1 bg-red-600 rounded-full animate-ping" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-red-300 rounded-full animate-pulse" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>

                      {/* Bottom particles */}
                      <div className="absolute w-1 h-1 bg-red-500 rounded-full animate-ping" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-red-700 rounded-full animate-pulse" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>

                      {/* Left particles */}
                      <div className="absolute w-1 h-1 bg-red-400 rounded-full animate-ping" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-red-500 rounded-full animate-pulse" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>
                    </div>
                    <svg className="w-full h-full relative z-10" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z"/>
                    </svg>
                  </div>
                </div>
                <h3 className="text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-red-400 transition-colors duration-300">
                  Criminal
                </h3>
                <p className="text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow">
                  Build your empire from the streets. Rob banks, run illegal businesses, and climb the criminal hierarchy.
                </p>
              </div>

              <div className="absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-red-500/30 group-hover:border-red-500 transition-colors duration-300 z-20"></div>
              <div className="absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-red-500/30 group-hover:border-red-500 transition-colors duration-300 z-20"></div>
            </div>

            {/* Police Character */}
            <div className="glass-enhanced rounded-xl p-4 sm:p-5 md:p-6 border border-blue-500/30 hover:border-blue-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-hexagon">
              <div className="absolute inset-0 bg-black/20 rounded-xl"></div>

              <div className="relative z-10 flex flex-col h-full">
                <div className="mb-3 text-blue-500 group-hover:text-white transition-all duration-300 relative icon-container">
                  <div className="w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center">
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                      {/* Top particles */}
                      <div className="absolute w-1 h-1 bg-blue-500 rounded-full animate-ping" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-blue-400 rounded-full animate-pulse" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>

                      {/* Right particles */}
                      <div className="absolute w-1 h-1 bg-blue-600 rounded-full animate-ping" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-blue-300 rounded-full animate-pulse" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>

                      {/* Bottom particles */}
                      <div className="absolute w-1 h-1 bg-blue-500 rounded-full animate-ping" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-blue-700 rounded-full animate-pulse" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>

                      {/* Left particles */}
                      <div className="absolute w-1 h-1 bg-blue-400 rounded-full animate-ping" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-blue-500 rounded-full animate-pulse" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>
                    </div>
                    <svg className="w-full h-full relative z-10" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217z" clipRule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <h3 className="text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
                  Legal
                </h3>
                <p className="text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow">
                  Work in the legal system. Practice law, represent clients, handle legal cases, and ensure justice is served.
                </p>
              </div>

              <div className="absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-blue-500/30 group-hover:border-blue-500 transition-colors duration-300 z-20"></div>
              <div className="absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-blue-500/30 group-hover:border-blue-500 transition-colors duration-300 z-20"></div>
            </div>

            {/* Civilian Character */}
            <div className="glass-enhanced rounded-xl p-4 sm:p-5 md:p-6 border border-green-500/30 hover:border-green-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-dots">
              <div className="absolute inset-0 bg-black/20 rounded-xl"></div>

              <div className="relative z-10 flex flex-col h-full">
                <div className="mb-3 text-green-500 group-hover:text-white transition-all duration-300 relative icon-container">
                  <div className="w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center">
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                      {/* Top particles */}
                      <div className="absolute w-1 h-1 bg-green-500 rounded-full animate-ping" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-green-400 rounded-full animate-pulse" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>

                      {/* Right particles */}
                      <div className="absolute w-1 h-1 bg-green-600 rounded-full animate-ping" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-green-300 rounded-full animate-pulse" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>

                      {/* Bottom particles */}
                      <div className="absolute w-1 h-1 bg-green-500 rounded-full animate-ping" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-green-700 rounded-full animate-pulse" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>

                      {/* Left particles */}
                      <div className="absolute w-1 h-1 bg-green-400 rounded-full animate-ping" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-green-500 rounded-full animate-pulse" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>
                    </div>
                    <svg className="w-full h-full relative z-10" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                    </svg>
                  </div>
                </div>
                <h3 className="text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-green-400 transition-colors duration-300">
                  Civilian
                </h3>
                <p className="text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow">
                  Live a normal life. Start businesses, buy properties, and build relationships in the community.
                </p>
              </div>

              <div className="absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-green-500/30 group-hover:border-green-500 transition-colors duration-300 z-20"></div>
              <div className="absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-green-500/30 group-hover:border-green-500 transition-colors duration-300 z-20"></div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8 px-2 sm:px-0">
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">80+</div>
              <div className="text-xs sm:text-sm md:text-base text-gray-400">Active Players</div>
            </div>
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">24/7</div>
              <div className="text-xs sm:text-sm md:text-base text-gray-400">Server Uptime</div>
            </div>
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">10+</div>
              <div className="text-xs sm:text-sm md:text-base text-gray-400">Custom Scripts</div>
            </div>
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">100%</div>
              <div className="text-xs sm:text-sm md:text-base text-gray-400">Telugu Community</div>
            </div>
          </div>
        </div>
      </section>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* Features Section */}
      <section id="features" className="py-12 md:py-20 relative bg-black/50">
        <div className="container mx-auto px-6 sm:px-12 md:px-16 lg:px-20 xl:px-24">
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg">
              <span className="text-neon-orange">UNIQUE</span> FEATURES
            </h2>
            <p className="text-sm sm:text-base md:text-lg text-gray-400 max-w-3xl mx-auto px-2">
              Discover what makes our server special with these exclusive features
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6 items-stretch">
            <FeatureCard
              title="24/7 Server Uptime"
              description="Our server is online 24/7, ensuring you can play whenever you want without interruptions."
              tiltDirection="left"
              pattern="circuit"
              className="animate-slide-in-left"
              icon={
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />

            <FeatureCard
              title="Realistic Economy"
              description="Experience a balanced and realistic economy system with multiple jobs and business opportunities."
              tiltDirection="right"
              pattern="hexagon"
              className="animate-slide-in-right"
              icon={
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />

            <FeatureCard
              title="Immersive Roleplay"
              description="Dive into a rich roleplay experience with custom scripts and scenarios designed for the Telugu community."
              tiltDirection="left"
              pattern="dots"
              className="animate-slide-in-left"
              icon={
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                </svg>
              }
            />

            <FeatureCard
              title="Friendly Community"
              description="Join a welcoming Telugu community that values respect, creativity, and fun roleplay experiences."
              tiltDirection="right"
              pattern="grid"
              className="animate-slide-in-right"
              icon={
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              }
            />

            <FeatureCard
              title="Supportive Staff"
              description="Our dedicated staff team is always ready to help, ensuring a smooth and enjoyable experience for all players."
              tiltDirection="left"
              pattern="circuit"
              className="animate-slide-in-left"
              icon={
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              }
            />

            <FeatureCard
              title="Active Development"
              description="Regular updates and new features keep the server fresh and exciting with continuous improvements."
              tiltDirection="right"
              pattern="hexagon"
              className="animate-slide-in-right"
              icon={
                <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
              }
            />
            </div>
          </div>
        </div>
      </section>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* Join Section */}
      <section id="join" className="py-8 sm:py-12 md:py-20 relative bg-black/50">
        <BouncingShapes />
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="glass rounded-lg p-4 sm:p-6 md:p-8 lg:p-12 border border-neon-orange/30 max-w-4xl mx-auto">
            <div className="text-center mb-4 sm:mb-6 md:mb-8">
              <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-display font-bold text-white mb-3 sm:mb-4 wave-heading-bg">
                <span className="text-neon-orange">JOIN</span> OUR SERVER
              </h2>
              <p className="text-xs sm:text-sm md:text-base text-gray-400 px-2">
                Ready to start your journey in the TGRS community? Follow these steps to join our server.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
              <div className="text-center p-2 sm:p-3 md:p-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3">
                  <span className="text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl">1</span>
                </div>
                <h3 className="text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base">Join Discord</h3>
                <p className="text-gray-400 text-xs sm:text-sm">Connect with our community on Discord for server updates and support.</p>
              </div>

              <div className="text-center p-2 sm:p-3 md:p-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3">
                  <span className="text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl">2</span>
                </div>
                <h3 className="text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base">Read Rules</h3>
                <p className="text-gray-400 text-xs sm:text-sm">Familiarize yourself with our server rules to ensure a positive experience.</p>
              </div>

              <div className="text-center p-2 sm:p-3 md:p-4 sm:col-span-2 md:col-span-1">
                <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3">
                  <span className="text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl">3</span>
                </div>
                <h3 className="text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base">Connect & Play</h3>
                <p className="text-gray-400 text-xs sm:text-sm">Use FiveM to connect to our server and start your roleplay journey.</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 justify-center px-2 sm:px-4 md:px-0">
              <Button href="https://discord.gg/GAMravHDnB" variant="primary" size="md" className="w-full sm:w-auto text-xs sm:text-sm md:text-base py-2 sm:py-3">
                Join Discord
              </Button>
              <Button href="/rules" variant="outline" size="md" className="w-full sm:w-auto text-xs sm:text-sm md:text-base py-2 sm:py-3">
                View Rules
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </>
  );
}
