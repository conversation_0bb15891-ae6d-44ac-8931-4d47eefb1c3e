import React from "react";

interface FeatureCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  className?: string;
  tiltDirection?: "left" | "right" | "none";
  pattern?: "dots" | "grid" | "hexagon" | "circuit" | "none";
}

const FeatureCard = ({
  title,
  description,
  icon,
  className = "",
  tiltDirection = "none",
  pattern = "circuit",
}: FeatureCardProps) => {
  const getTiltClass = () => {
    switch (tiltDirection) {
      case "left":
        return "card-tilt-left";
      case "right":
        return "card-tilt-right";
      default:
        return "";
    }
  };

  const getPatternClass = () => {
    switch (pattern) {
      case "dots":
        return "pattern-dots";
      case "grid":
        return "pattern-grid";
      case "hexagon":
        return "pattern-hexagon";
      case "circuit":
        return "pattern-circuit";
      default:
        return "";
    }
  };

  return (
    <div className={`glass-enhanced rounded-lg p-4 sm:p-4 md:p-5 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col ${getTiltClass()} ${getPatternClass()} ${className}`}>
      {/* Background overlay for pattern */}
      <div className="absolute inset-0 bg-black/20 rounded-xl"></div>

      {/* Content */}
      <div className="relative z-10 flex flex-col h-full">
        {icon && (
          <div className="mb-3 sm:mb-4 md:mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container">
            <div className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center">
              {/* Animated particles around the icon */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                {/* Top particles */}
                <div className="absolute w-1 h-1 bg-neon-orange rounded-full animate-ping" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>
                <div className="absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>

                {/* Right particles */}
                <div className="absolute w-1 h-1 bg-orange-400 rounded-full animate-ping" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>
                <div className="absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>

                {/* Bottom particles */}
                <div className="absolute w-1 h-1 bg-neon-orange rounded-full animate-ping" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>
                <div className="absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>

                {/* Left particles */}
                <div className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>
                <div className="absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>
              </div>
              <div className="w-full h-full relative z-10">
                {icon}
              </div>
            </div>
          </div>
        )}
        <h3 className="text-sm sm:text-base md:text-lg font-display font-bold text-white mb-2 sm:mb-3 md:mb-2 group-hover:text-neon-orange transition-colors duration-300">
          {title}
        </h3>
        <p className="text-xs sm:text-sm md:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow">
          {description}
        </p>
      </div>

      {/* Decorative corner elements */}
      <div className="absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"></div>
      <div className="absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"></div>
    </div>
  );
};

export default FeatureCard;
