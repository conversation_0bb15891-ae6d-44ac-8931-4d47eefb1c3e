import React from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import FeatureCard from "@/components/FeatureCard";
import Button from "@/components/Button";

export default function Features() {
  return (
    <>
      <Navbar />

      {/* Header */}
      <div className="pt-24 pb-12 md:pt-32 md:pb-20 relative bg-black">
        <div className="absolute inset-0 pattern-circuit opacity-5"></div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg">
              <span className="text-neon-orange">SERVER</span> FEATURES
            </h1>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Discover what makes TGRS the premier Telugu community FiveM roleplay server.
            </p>
          </div>
        </div>
      </div>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* Features Overview */}
      <section className="py-20 relative bg-black/50">
        <div className="container mx-auto px-6 sm:px-12 md:px-16 lg:px-20 xl:px-24">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mb-12 md:mb-16">
              <div className="glass rounded-lg p-6 border border-neon-orange/20 text-center">
                <div className="w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-neon-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-display font-semibold text-white mb-2">
                  Optimized Performance
                </h3>
                <p className="text-gray-400">
                  Our server is optimized for smooth gameplay with minimal lag, ensuring a seamless roleplay experience.
                </p>
              </div>

              <div className="glass rounded-lg p-6 border border-neon-orange/20 text-center">
                <div className="w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-neon-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-display font-semibold text-white mb-2">
                  Telugu Community
                </h3>
                <p className="text-gray-400">
                  A dedicated server for Telugu gamers, creating a unique cultural connection within the roleplay experience.
                </p>
              </div>

              <div className="glass rounded-lg p-6 border border-neon-orange/20 text-center">
                <div className="w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-neon-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                </div>
                <h3 className="text-xl font-display font-semibold text-white mb-2">
                  Custom Scripts
                </h3>
                <p className="text-gray-400">
                  Unique and exclusive scripts developed specifically for our server to enhance the roleplay experience.
                </p>
              </div>
            </div>
          </div>

          {/* Section Divider */}
          <div className="section-divider"></div>

          {/* Gameplay Features */}
          <div className="mb-12 md:mb-16 pt-16 md:pt-20">
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg">
                <span className="text-neon-orange">GAMEPLAY</span> FEATURES
              </h2>
              <p className="text-gray-400 max-w-3xl mx-auto px-2">
                Immersive gameplay elements that make TGRS unique
              </p>
            </div>

            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6">
                <FeatureCard
                title="Custom Character Creation"
                description="Create a unique character with extensive customization options tailored for diverse appearances."
                icon={
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                }
              />

              <FeatureCard
                title="Advanced Economy System"
                description="A balanced economy with multiple jobs, businesses, and investment opportunities."
                icon={
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
              />

              <FeatureCard
                title="Custom Properties"
                description="Own and customize properties throughout the city, from apartments to businesses."
                icon={
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                }
              />

              <FeatureCard
                title="Realistic Vehicle System"
                description="Detailed vehicle mechanics including fuel, damage, and maintenance requirements."
                icon={
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                }
              />

              <FeatureCard
                title="Advanced Job System"
                description="Multiple legal and illegal career paths with progression and unique gameplay mechanics."
                icon={
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                }
              />

              <FeatureCard
                title="Dynamic Weather System"
                description="Realistic and dynamic weather patterns that affect gameplay and visibility."
                icon={
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                  </svg>
                }
              />
              </div>
            </div>
          </div>

          {/* Section Divider */}
          <div className="section-divider"></div>

          {/* Community Features */}
          <div className="mb-12 md:mb-16 pt-12 md:pt-16">
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg">
                <span className="text-neon-orange">COMMUNITY</span> FEATURES
              </h2>
              <p className="text-sm sm:text-base md:text-lg text-gray-400 max-w-3xl mx-auto px-4">
                Elements that enhance our community experience
              </p>
            </div>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-stretch">
                <div className="glass-enhanced rounded-xl p-6 sm:p-8 border border-neon-orange/30 hover:border-neon-orange/60 transition-all duration-500 hover:shadow-neon-strong group h-full">
                  <h3 className="text-xl sm:text-2xl font-display font-semibold text-white mb-4 sm:mb-6 flex items-center group-hover:text-neon-orange transition-colors duration-300">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 mr-3 sm:mr-4 text-neon-orange group-hover:text-white transition-colors duration-300 relative">
                      <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    Active Discord Community
                  </h3>
                  <p className="text-sm sm:text-base text-gray-400 mb-4 sm:mb-6 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed">
                    Our Discord server is the hub for all community activities, featuring:
                  </p>
                  <ul className="space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300">
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Dedicated support channels</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Community events and announcements</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Character development resources</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Voice channels for out-of-game socializing</span>
                    </li>
                  </ul>
                </div>

                <div className="glass-enhanced rounded-xl p-6 sm:p-8 border border-neon-orange/30 hover:border-neon-orange/60 transition-all duration-500 hover:shadow-neon-strong group h-full">
                  <h3 className="text-xl sm:text-2xl font-display font-semibold text-white mb-4 sm:mb-6 flex items-center group-hover:text-neon-orange transition-colors duration-300">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 mr-3 sm:mr-4 text-neon-orange group-hover:text-white transition-colors duration-300 relative">
                      <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    Regular Events
                  </h3>
                  <p className="text-sm sm:text-base text-gray-400 mb-4 sm:mb-6 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed">
                    We host a variety of events to keep the community engaged:
                  </p>
                  <ul className="space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300">
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Weekly car meets and races</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Special holiday celebrations</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Community challenges with prizes</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-neon-orange mr-3 mt-1 text-lg">•</span>
                      <span className="leading-relaxed">Roleplay scenarios and storylines</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <p className="text-gray-400 mb-6">
              Ready to experience all these features and more? Join our server today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button href="#" variant="primary" size="lg">
                Join Discord
              </Button>
              <Button href="/rules" variant="outline" size="lg">
                View Rules
              </Button>
            </div>
          </div>
          </div>
        </div>
      </section>

      <Footer />
    </>
  );
}
