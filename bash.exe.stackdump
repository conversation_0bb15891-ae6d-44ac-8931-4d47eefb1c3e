Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FEBA
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210285FF9, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBB0  0002100690B4 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE90  00021006A49D (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF86A100000 ntdll.dll
7FF8687F0000 KERNEL32.DLL
7FF8679B0000 KERNELBASE.dll
7FF868940000 USER32.dll
7FF8675D0000 win32u.dll
000210040000 msys-2.0.dll
7FF869580000 GDI32.dll
7FF867D80000 gdi32full.dll
7FF867780000 msvcp_win.dll
7FF867290000 ucrtbase.dll
7FF869D60000 advapi32.dll
7FF8699C0000 msvcrt.dll
7FF867F40000 sechost.dll
7FF868B10000 RPCRT4.dll
7FF866880000 CRYPTBASE.DLL
7FF8674A0000 bcryptPrimitives.dll
7FF868000000 IMM32.DLL
